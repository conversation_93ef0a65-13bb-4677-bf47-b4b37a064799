This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2026/dev/Arch Linux) (preloaded format=pdflatex 2025.7.9)  17 JUL 2025 10:15
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**costo_boleta_orig_interpretacion_representacion_n2_v1.tex
(./costo_boleta_orig_interpretacion_representacion_n2_v1.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(/usr/share/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(/usr/share/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2025/02/14 v25.4 The multilingual framework for pdfLaTeX, LuaLaT
eX and XeLaTeX
\babel@savecnt=\count270
\U@D=\dimen142
\l@unhyphenated=\language6

(/usr/share/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count271

(/usr/share/texmf-dist/tex/generic/babel-spanish/spanish.ldf
Language: spanish.ldf 2021/05/27 v5.0q Spanish support from the babel system
\es@quottoks=\toks19
\es@quotdepth=\count272
Package babel Info: Making " an active character on input line 570.
Package babel Info: Making . an active character on input line 675.
Package babel Info: Making < an active character on input line 722.
Package babel Info: Making > an active character on input line 722.
))
(/usr/share/texmf-dist/tex/generic/babel/locale/es/babel-spanish.tex
Package babel Info: Importing font and identification data for spanish
(babel)             from babel-es.ini. Reported on input line 11.
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen143
))
(/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen144
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count273
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count274
\leftroot@=\count275
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count276
\DOTSCASE@=\count277
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen145
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count278
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count279
\dotsspace@=\muskip17
\c@parentequation=\count280
\dspbrk@lvl=\count281
\tag@help=\toks21
\row@=\count282
\column@=\count283
\maxfields@=\count284
\andhelp@=\toks22
\eqnshift@=\dimen146
\alignsep@=\dimen147
\tagshift@=\dimen148
\tagwidth@=\dimen149
\totwidth@=\dimen150
\lineht@=\dimen151
\@envbody=\toks23
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/share/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/share/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks25
)
(/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(/usr/share/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen152
\Gin@req@width=\dimen153
)
(/usr/share/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip54
\enit@outerparindent=\dimen154
\enit@toks=\toks26
\enit@inbox=\box54
\enit@count@id=\count285
\enitdp@description=\count286
)
(/usr/share/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2024-01-22 v1.5x LaTeX2e package for verbatim enhancements
\every@verbatim=\toks27
\verbatim@line=\toks28
\verbatim@in@stream=\read3
)
(/usr/share/texmf-dist/tex/latex/graphics/color.sty
Package: color 2024/06/23 v1.3e Standard LaTeX Color (DPC)

(/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx))
(/usr/share/texmf/tex/latex/Sweave.sty
Package: Sweave 

(/usr/share/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
(/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
(/usr/share/texmf-dist/tex/latex/fancyvrb/fancyvrb.sty
Package: fancyvrb 2024/01/20 4.5c verbatim text (tvz,hv)
\FV@CodeLineNo=\count287
\FV@InFile=\read4
\FV@TabBox=\box55
\c@FancyVerbLine=\count288
\FV@StepNumber=\count289
\FV@OutFile=\write3
)
(/usr/share/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
(/usr/share/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
))
(/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count290
\l__pdf_internal_box=\box56
)
(./costo_boleta_orig_interpretacion_representacion_n2_v1.aux
LaTeX Info: Redefining \. on input line 8.
LaTeX Info: Redefining \% on input line 8.
)
\openout1 = `costo_boleta_orig_interpretacion_representacion_n2_v1.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Info: Redefining \. on input line 24.
LaTeX Info: Redefining \% on input line 24.

(/usr/share/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count291
\scratchdimen=\dimen155
\scratchbox=\box57
\nofMPsegments=\count292
\nofMParguments=\count293
\everyMPshowfont=\toks29
\MPscratchCnt=\count294
\MPscratchDim=\dimen156
\MPnumerator=\count295
\makeMPintoPDFobject=\count296
\everyMPtoPDFconversion=\toks30
) (/usr/share/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf

(/usr/share/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(/usr/share/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)

(/usr/share/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
))
(/usr/share/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(/usr/share/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(/usr/share/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
(/usr/share/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPE
G,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.

(/usr/share/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
(./costo_boleta_orig_interpretacion_representacion_n2_v1-concordance.tex)
LaTeX Font Info:    Trying to load font information for U+msa on input line 35.


(/usr/share/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 35.


(/usr/share/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
<costo_boleta_orig_interpretacion_representacion_n2_v1-002.pdf, id=2, 361.35pt 
x 289.08pt>
File: costo_boleta_orig_interpretacion_representacion_n2_v1-002.pdf Graphic fil
e (type pdf)
<use costo_boleta_orig_interpretacion_representacion_n2_v1-002.pdf>
Package pdftex.def Info: costo_boleta_orig_interpretacion_representacion_n2_v1-
002.pdf  used on input line 62.
(pdftex.def)             Requested size: 276.00105pt x 220.8014pt.
<costo_boleta_orig_interpretacion_representacion_n2_v1-003.pdf, id=3, 361.35pt 
x 289.08pt>
File: costo_boleta_orig_interpretacion_representacion_n2_v1-003.pdf Graphic fil
e (type pdf)
<use costo_boleta_orig_interpretacion_representacion_n2_v1-003.pdf>
Package pdftex.def Info: costo_boleta_orig_interpretacion_representacion_n2_v1-
003.pdf  used on input line 65.
(pdftex.def)             Requested size: 276.00105pt x 220.8014pt.


[1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texmf-dist/fonts
/enc/dvips/cm-super/cm-super-t1.enc} <./costo_boleta_orig_interpretacion_repres
entacion_n2_v1-002.pdf>]
<costo_boleta_orig_interpretacion_representacion_n2_v1-004.pdf, id=21, 361.35pt
 x 289.08pt>
File: costo_boleta_orig_interpretacion_representacion_n2_v1-004.pdf Graphic fil
e (type pdf)
<use costo_boleta_orig_interpretacion_representacion_n2_v1-004.pdf>
Package pdftex.def Info: costo_boleta_orig_interpretacion_representacion_n2_v1-
004.pdf  used on input line 68.
(pdftex.def)             Requested size: 276.00105pt x 220.8014pt.
<costo_boleta_orig_interpretacion_representacion_n2_v1-005.pdf, id=22, 361.35pt
 x 289.08pt>
File: costo_boleta_orig_interpretacion_representacion_n2_v1-005.pdf Graphic fil
e (type pdf)
<use costo_boleta_orig_interpretacion_representacion_n2_v1-005.pdf>
Package pdftex.def Info: costo_boleta_orig_interpretacion_representacion_n2_v1-
005.pdf  used on input line 71.
(pdftex.def)             Requested size: 276.00105pt x 220.8014pt.


[2 <./costo_boleta_orig_interpretacion_representacion_n2_v1-003.pdf> <./costo_b
oleta_orig_interpretacion_representacion_n2_v1-004.pdf>]

[3 <./costo_boleta_orig_interpretacion_representacion_n2_v1-005.pdf>]
(./costo_boleta_orig_interpretacion_representacion_n2_v1.aux
LaTeX Info: Redefining \. on input line 8.
LaTeX Info: Redefining \% on input line 8.
)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 ) 
Here is how much of TeX's memory you used:
 5811 strings out of 475109
 93558 string characters out of 5765326
 484621 words of memory out of 5000000
 28738 multiletter control sequences out of 15000+600000
 562075 words of font info for 46 fonts, out of 8000000 for 9000
 40 hyphenation exceptions out of 8191
 59i,6n,67p,279b,400s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf-dist/fonts/type1/public/cm-super/sfbx1000.pfb></usr/share/t
exmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb>
Output written on costo_boleta_orig_interpretacion_representacion_n2_v1.pdf (3 
pages, 56885 bytes).
PDF statistics:
 58 PDF objects out of 1000 (max. 8388607)
 39 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 25 words of extra memory for PDF output out of 10000 (max. 10000000)

